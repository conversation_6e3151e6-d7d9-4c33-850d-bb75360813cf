'use client';

import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Switch from '@mui/material/Switch';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';
import InputAdornment from '@mui/material/InputAdornment';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Field } from 'src/components/hook-form';

// ----------------------------------------------------------------------

export default function ProductPricingAdvanced() {
  const { watch, setValue } = useFormContext();
  const [includeTaxes, setIncludeTaxes] = useState(false);

  const values = watch();

  const handleChangeIncludeTaxes = (event) => {
    const checked = event.target.checked;
    setIncludeTaxes(checked);
    setValue('includeTaxes', checked);

    // Reset taxes value when toggling
    if (checked) {
      setValue('taxes', null);
    }
  };

  return (
    <Card>
      <CardHeader title="Giá và thuế" />

      <CardContent>
        <Stack spacing={3}>
          {/* Price */}
          <Field.CurrencyInput
            name="price"
            label="Giá bán"
            placeholder="0"
            required
            helperText="Giá bán thực tế cho khách hàng"
          />

          {/* Compare At Price */}
          <Field.CurrencyInput
            name="compareAtPrice"
            label="Giá so sánh"
            placeholder="0"
            helperText="Giá gốc trước khi giảm giá (tùy chọn)"
          />

          {/* Cost Price */}
          <Field.CurrencyInput
            name="costPrice"
            label="Giá vốn"
            placeholder="0"
            helperText="Giá vốn để tính toán lợi nhuận (không hiển thị công khai)"
          />

          {/* Tax Settings */}
          <FormControlLabel
            control={
              <Switch
                id="toggle-taxes"
                checked={includeTaxes}
                onChange={handleChangeIncludeTaxes}
              />
            }
            label="Giá đã bao gồm thuế"
          />

          {!includeTaxes && (
            <Field.Text
              name="taxes"
              label="Thuế (%)"
              placeholder="0"
              type="number"
              slotProps={{
                inputLabel: { shrink: true },
                input: {
                  startAdornment: (
                    <InputAdornment position="start" sx={{ mr: 0.75 }}>
                      <Box component="span" sx={{ color: 'text.disabled' }}>
                        %
                      </Box>
                    </InputAdornment>
                  ),
                },
              }}
              helperText="Thuế VAT hoặc thuế khác (VD: 10 cho 10%)"
            />
          )}

          {/* Price Summary */}
          {values.price && (
            <Box sx={{ p: 2, bgcolor: 'background.neutral', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                Tóm tắt giá
              </Typography>
              <Stack spacing={1}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Giá bán:</Typography>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                    {Number(values.price).toLocaleString('vi-VN')} ₫
                  </Typography>
                </Box>
                {values.compareAtPrice && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Giá so sánh:</Typography>
                    <Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
                      {Number(values.compareAtPrice).toLocaleString('vi-VN')} ₫
                    </Typography>
                  </Box>
                )}
                {values.compareAtPrice && values.price && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Tiết kiệm:</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'error.main' }}>
                      {(Number(values.compareAtPrice) - Number(values.price)).toLocaleString('vi-VN')} ₫
                      ({Math.round(((Number(values.compareAtPrice) - Number(values.price)) / Number(values.compareAtPrice)) * 100)}%)
                    </Typography>
                  </Box>
                )}
                {values.taxes && !includeTaxes && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Thuế ({values.taxes}%):</Typography>
                    <Typography variant="body2">
                      {((values.price * values.taxes) / 100).toLocaleString('vi-VN')} ₫
                    </Typography>
                  </Box>
                )}
              </Stack>
            </Box>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
}
