'use client';

import PropTypes from 'prop-types';
import { useTabs } from 'minimal-shared/hooks';
import { useMemo, useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Table from '@mui/material/Table';
import Paper from '@mui/material/Paper';
import Skeleton from '@mui/material/Skeleton';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TablePagination from '@mui/material/TablePagination';

import { Scrollbar } from 'src/components/scrollbar';
import { EmptyContent } from 'src/components/empty-content';
import { useBusinessConfig } from 'src/components/business-aware';
import { TableHeadCustom } from 'src/components/table/table-head-custom';

import ProductTableRow from './product-table-row';
import ProductTableToolbar from './product-table-toolbar';

// ----------------------------------------------------------------------

// Định nghĩa các tab - sẽ được cập nhật dựa trên business type
const getTabsForBusinessType = (businessType) => [
  { value: 'active', label: 'Đang hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' },
  {
    value: 'all',
    label: businessType === 'services' ? 'Tất cả dịch vụ' :
           businessType === 'digital' ? 'Tất cả sản phẩm số' :
           'Tất cả sản phẩm'
  },
];

export default function ProductList({
  products,
  isLoading,
  onEdit,
  onDelete,
  onToggleActive,
  onDeleteSelected,
  onInventoryUpdated,
}) {
  const [page, setPage] = useState(0);
  const [order, setOrder] = useState('desc');
  const [orderBy, setOrderBy] = useState('createdAt');
  const [filterName, setFilterName] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selected, setSelected] = useState([]);

  // Business configuration
  const { businessType, isFeatureEnabled } = useBusinessConfig();

  // Expose resetSelected function to parent component
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.resetProductSelection = () => {
        setSelected([]);
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete window.resetProductSelection;
      }
    };
  }, []);

  // Tab state
  const tabs = useTabs('active');

  // Xử lý sắp xếp
  const handleRequestSort = useCallback(
    (property) => {
      const isAsc = orderBy === property && order === 'asc';
      setOrder(isAsc ? 'desc' : 'asc');
      setOrderBy(property);
    },
    [order, orderBy]
  );

  // Xử lý thay đổi trang
  const handleChangePage = useCallback((_, newPage) => {
    setPage(newPage);
  }, []);

  // Xử lý thay đổi số dòng mỗi trang
  const handleChangeRowsPerPage = useCallback((event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  // Xử lý lọc theo tên
  const handleFilterByName = useCallback((value) => {
    setFilterName(value);
    setPage(0);
  }, []);

  // Lọc và sắp xếp dữ liệu
  const filteredProducts = useMemo(() => {
    if (!products || !Array.isArray(products)) return [];

    return products
      .filter((product) => {
        // Lọc theo tên
        const nameMatch =
          !filterName || product.name.toLowerCase().includes(filterName.toLowerCase());

        // Lọc theo tab
        let tabMatch = true;
        if (tabs.value === 'active') {
          tabMatch = product.isActive === true;
        } else if (tabs.value === 'inactive') {
          tabMatch = product.isActive === false;
        }

        return nameMatch && tabMatch;
      })
      .sort((a, b) => {
        const valueA = a[orderBy] || '';
        const valueB = b[orderBy] || '';

        if (order === 'asc') {
          return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
        }
        return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
      });
  }, [products, filterName, order, orderBy, tabs.value]);

  // Xử lý chọn tất cả sản phẩm
  const handleSelectAllRows = useCallback(
    (checked) => {
      if (checked) {
        const newSelected = filteredProducts.map((product) => product.id);
        setSelected(newSelected);
        return;
      }
      setSelected([]);
    },
    [filteredProducts]
  );

  // Xử lý chọn một sản phẩm
  const handleSelectRow = useCallback(
    (id) => {
      const selectedIndex = selected.indexOf(id);
      let newSelected = [];

      if (selectedIndex === -1) {
        newSelected = [...selected, id];
      } else {
        newSelected = selected.filter((item) => item !== id);
      }

      setSelected(newSelected);
    },
    [selected]
  );

  // Xử lý xóa các sản phẩm đã chọn
  const handleDeleteSelectedProducts = useCallback(() => {
    if (onDeleteSelected && selected.length > 0) {
      onDeleteSelected(selected);
    }
  }, [onDeleteSelected, selected]);

  // Tính toán số trang trống
  const emptyRows = useMemo(
    () => (page > 0 ? Math.max(0, (1 + page) * rowsPerPage - filteredProducts.length) : 0),
    [page, rowsPerPage, filteredProducts.length]
  );

  // Kiểm tra nếu không có dữ liệu
  const isNotFound = useMemo(
    () => !isLoading && (!filteredProducts.length || (filterName && !filteredProducts.length)),
    [isLoading, filteredProducts.length, filterName]
  );

  // Định nghĩa các cột cho bảng dựa trên business type
  const TABLE_HEAD = useMemo(() => {
    const baseColumns = [
      { id: 'name', label: businessType === 'services' ? 'Tên dịch vụ' : 'Tên sản phẩm' },
      { id: 'price', label: 'Giá bán' },
    ];

    // Add business-specific columns
    if (isFeatureEnabled('inventory')) {
      baseColumns.push({ id: 'stockQuantity', label: 'Tồn kho' });
    }

    if (businessType === 'digital') {
      baseColumns.push({ id: 'downloadCount', label: 'Lượt tải' });
    }

    if (businessType === 'services') {
      baseColumns.push({ id: 'duration', label: 'Thời lượng' });
    }

    baseColumns.push(
      { id: 'type', label: 'Loại' },
      { id: 'updatedAt', label: 'Ngày cập nhật' },
      { id: '', label: 'Thao tác', align: 'right' }
    );

    return baseColumns;
  }, [businessType, isFeatureEnabled]);

  // Hiển thị skeleton khi đang tải
  if (isLoading) {
    return (
      <Box sx={{ p: 3 }}>
        <Skeleton height={80} sx={{ mb: 1 }} />
        <Skeleton height={40} sx={{ mb: 1 }} />
        <Skeleton height={40} sx={{ mb: 1 }} />
        <Skeleton height={40} sx={{ mb: 1 }} />
        <Skeleton height={40} sx={{ mb: 1 }} />
        <Skeleton height={40} sx={{ mb: 1 }} />
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ width: '100%', mb: 2 }}>
        <ProductTableToolbar
          filterName={filterName}
          onFilterName={handleFilterByName}
          numSelected={selected.length}
          onDeleteSelected={handleDeleteSelectedProducts}
        />

        <Tabs
          value={tabs.value}
          onChange={tabs.onChange}
          sx={{
            px: 2,
            bgcolor: 'background.neutral',
            borderRadius: 1,
            mb: 2,
            mx: 2,
          }}
        >
          {getTabsForBusinessType(businessType).map((tab) => (
            <Tab
              key={tab.value}
              value={tab.value}
              label={tab.label}
              iconPosition="end"
              icon={
                <Box
                  component="span"
                  sx={{
                    ml: 1,
                    width: 28,
                    height: 28,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderRadius: '50%',
                    bgcolor: 'background.paper',
                    ...(tab.value === tabs.value && {
                      bgcolor: 'primary.main',
                      color: 'primary.contrastText',
                    }),
                  }}
                >
                  {tab.value === 'all'
                    ? products?.length || 0
                    : tab.value === 'active'
                      ? products?.filter((item) => item.isActive === true)?.length || 0
                      : products?.filter((item) => item.isActive === false)?.length || 0}
                </Box>
              }
            />
          ))}
        </Tabs>

        <TableContainer component={Paper}>
          <Scrollbar>
            <Table sx={{ minWidth: 800 }}>
              <TableHeadCustom
                order={order}
                orderBy={orderBy}
                headCells={TABLE_HEAD}
                rowCount={filteredProducts.length}
                numSelected={selected.length}
                onSort={handleRequestSort}
                onSelectAllRows={handleSelectAllRows}
              />

              <TableBody>
                {isNotFound ? (
                  <TableRow>
                    <TableCell align="center" colSpan={7} sx={{ py: 3 }}>
                      <EmptyContent filled title="Không tìm thấy sản phẩm nào" />
                    </TableCell>
                  </TableRow>
                ) : (
                  <>
                    {filteredProducts
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((product) => (
                        <ProductTableRow
                          key={product.id}
                          product={product}
                          selected={selected.includes(product.id)}
                          onSelectRow={() => handleSelectRow(product.id)}
                          onEdit={() => onEdit(product.id)}
                          onDelete={() => onDelete(product.id)}
                          onToggleActive={onToggleActive}
                          onInventoryUpdated={onInventoryUpdated}
                        />
                      ))}

                    {emptyRows > 0 && (
                      <TableRow style={{ height: 53 * emptyRows }}>
                        <TableCell colSpan={7} />
                      </TableRow>
                    )}
                  </>
                )}
              </TableBody>
            </Table>
          </Scrollbar>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredProducts.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Số dòng mỗi trang:"
          labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
        />
      </Paper>
    </Box>
  );
}

ProductList.propTypes = {
  products: PropTypes.array,
  isLoading: PropTypes.bool,
  onEdit: PropTypes.func,
  onDelete: PropTypes.func,
  onToggleActive: PropTypes.func,
  onDeleteSelected: PropTypes.func,
  onInventoryUpdated: PropTypes.func,
};

ProductList.defaultProps = {
  products: [],
  isLoading: false,
  onEdit: () => {},
  onDelete: () => {},
  onToggleActive: () => {},
  onDeleteSelected: () => {},
  onInventoryUpdated: () => {},
};
